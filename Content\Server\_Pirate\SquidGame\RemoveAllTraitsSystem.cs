using Content.Server.Traits;
using Content.Shared.Traits;
using Robust.Shared.GameObjects;

namespace Content.Server._Pirate.SquidGame;

/// <summary>
/// System that handles removing all traits from entities with RemoveAllTraitsComponent.
/// </summary>
public sealed class RemoveAllTraitsSystem : EntitySystem
{
    [Dependency] private readonly TraitSystem _traitSystem = default!;
    private ISawmill _sawmill = default!;

    public override void Initialize()
    {
        base.Initialize();
        _sawmill = Logger.GetSawmill("pirate.squidgame");
        
        SubscribeLocalEvent<RemoveAllTraitsComponent, ComponentStartup>(OnComponentStartup);
    }

    private void OnComponentStartup(EntityUid uid, RemoveAllTraitsComponent component, ComponentStartup args)
    {
        // Prevent multiple removals
        if (component.TraitsRemoved)
            return;

        // Get all traits currently on the entity
        if (!TryComp<TraitsComponent>(uid, out var traitsComponent))
        {
            _sawmill.Debug($"Entity {uid} has no traits to remove");
            component.TraitsRemoved = true;
            return;
        }

        // Create a copy of the traits list to avoid modification during iteration
        var traitsToRemove = new List<string>(traitsComponent.Traits);

        // Remove all traits
        foreach (var traitId in traitsToRemove)
        {
            _traitSystem.RemoveTrait(uid, traitId);
            _sawmill.Debug($"Removed trait {traitId} from entity {uid}");
        }

        _sawmill.Info($"Removed {traitsToRemove.Count} traits from Squid Game participant {uid}");
        component.TraitsRemoved = true;
    }
}
