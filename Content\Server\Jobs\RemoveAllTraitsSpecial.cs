
using Content.Server.Traits;
using Content.Shared.Roles;
using Content.Shared.Traits;
using JetBrains.Annotations;
using Robust.Shared.IoC;
using Robust.Shared.Serialization.Manager.Attributes;

namespace Content.Server.Jobs;

/// <summary>
/// Job special that removes all traits from a character to ensure equal opportunities.
/// Used for event jobs where all players should have the same starting conditions.
/// </summary>
[UsedImplicitly]
[DataDefinition]
public sealed partial class RemoveAllTraitsSpecial : JobSpecial
{
    private ISawmill _sawmill = default!;

    public override void AfterEquip(EntityUid mob)
    {
        var entityManager = IoCManager.Resolve<IEntityManager>();
        var traitSystem = entityManager.System<TraitSystem>();
        _sawmill = Logger.GetSawmill("pirate.squidgame");

        // Get all traits currently on the entity
        if (!entityManager.TryGetComponent<TraitsComponent>(mob, out var traitsComponent))
        {
            _sawmill.Debug($"Entity {mob} has no traits to remove");
            return;
        }

        // Create a copy of the traits list to avoid modification during iteration
        var traitsToRemove = new List<string>(traitsComponent.Traits);

        // Remove all traits
        foreach (var traitId in traitsToRemove)
        {
            traitSystem.RemoveTrait(mob, traitId);
            _sawmill.Debug($"Removed trait {traitId} from entity {mob}");
        }

        _sawmill.Info($"Removed {traitsToRemove.Count} traits from Squid Game participant {mob}");
    }
}
